# KobiPanel - Proje <PERSON>rme Yol Haritası

## 🎯 Proje <PERSON>zeti
**Proje:** Aydın <PERSON>j <PERSON>  
**Backend:** Python FastAPI + SQLAlchemy + SQLite  
**Frontend:** React/Next.js + TypeScript + Tailwind CSS + ShadCN/UI 
**Geliştirme Yaklaşımı:** Aşamalı, modüler geliştirme

## 📋 Geliştirme Aşamaları

### 🔧 AŞAMA 1: Backend Altyapı ve Temel Modüller (Chat 1)
**Süre:** 1-2 hafta  
**Hedef:** Temel backend altyapısını kurmak ve core modülleri geliştirmek

#### Yapılacaklar:
1. **Proje <PERSON>ru<PERSON>u:**
   - FastAPI proje yapısı oluşturma
   - Virtual environment kurulumu
   - Gerekli kütüphanelerin yüklenmesi (requirements.txt)
   - Klasör yapısı organizasyonu

2. **Veritabanı Altyapısı:**
   - SQLAlchemy modelleri oluşturma
   - Alembic migration sistemi kurulumu
   - SQLite veritabanı bağlantısı
   - İlk migration dosyalarının oluşturulması

3. **Temel Modeller:**
   - User (Kullanıcı) modeli
   - Il, Ilce, Mahalle (Adres) modelleri
   - Customer (Müşteri) modeli
   - Product (Ürün) modeli

4. **Authentication Sistemi:**
   - JWT token sistemi
   - Login/logout endpoints
   - Password hashing (passlib)
   - Middleware ve dependency injection

5. **Temel CRUD Endpoints:**
   - Kullanıcı yönetimi endpoints
   - Müşteri yönetimi endpoints
   - Ürün yönetimi endpoints
   - Adres endpoints (cascade dropdown için)

#### Çıktılar:
- Çalışan FastAPI backend
- Temel veritabanı şeması
- Authentication sistemi
- Temel CRUD operasyonları
- API dokümantasyonu (Swagger)

---

### 🏪 AŞAMA 2: İş Mantığı Modülleri (Chat 2)
**Süre:** 1-2 hafta  
**Hedef:** Satış, gider, not yönetimi modüllerini tamamlamak

#### Yapılacaklar:
1. **Satış Yönetimi:**
   - Sale (Satış) modeli ve ilişkileri
   - Satış CRUD endpoints
   - Stok kontrolü mantığı
   - Teslim durumu takibi
   - Satış özeti endpoints

2. **Gider Yönetimi:**
   - Expense (Gider) modeli
   - Gider CRUD endpoints
   - Kategori sistemi
   - Tarih bazlı filtreleme

3. **Not Yönetimi:**
   - Note (Not) modeli
   - Not CRUD endpoints
   - Arama ve filtreleme

4. **İş Mantığı Validasyonları:**
   - Pydantic schemas
   - Business rule validations
   - Error handling

#### Çıktılar:
- Satış yönetimi sistemi
- Gider takip sistemi
- Not yönetimi
- Gelişmiş validasyon sistemi

---

### 🚗 AŞAMA 3: Araç ve Biçme Modülleri (Chat 3)
**Süre:** 1-2 hafta  
**Hedef:** Araç takibi ve biçme işleri modüllerini geliştirmek

#### Yapılacaklar:
1. **Araç Yönetimi:**
   - Vehicle (Araç) modeli
   - Fuel (Yakıt) modeli ve ilişkileri
   - Araç CRUD endpoints
   - Yakıt takip endpoints
   - Vize/sigorta/egzoz hatırlatma sistemi

2. **Biçme İşleri:**
   - CutField (Biçilen Tarla) modeli
   - WeighingSlip (Kantar Fişi) modeli
   - Biçme CRUD endpoints
   - Kantar fişi endpoints
   - Hasat miktarı hesaplamaları

3. **İlişkisel Mantık:**
   - Kantar fişi → Ürün stok entegrasyonu
   - Tarla → Hasat miktarı güncellemeleri
   - FIFO stok mantığı

#### Çıktılar:
- Araç takip sistemi
- Yakıt yönetimi
- Biçme işleri sistemi
- Stok entegrasyonu

---

### 📊 AŞAMA 4: Raporlama ve Dashboard (Chat 4)
**Süre:** 1 hafta  
**Hedef:** Dashboard ve raporlama sistemini tamamlamak

#### Yapılacaklar:
1. **Dashboard Endpoints:**
   - İstatistik kartları için endpoints
   - Aylık analizler
   - Son işlemler listeleri
   - Hatırlatma sistemi endpoints

2. **Raporlama Sistemi:**
   - Satış raporları
   - Gider raporları
   - Müşteri analizleri
   - Ürün raporları
   - Araç raporları
   - Biçme raporları

3. **Filtreleme ve Aggregation:**
   - Tarih aralığı filtreleri
   - Müşteri/ürün bazlı filtreler
   - Toplam/ortalama hesaplamaları

#### Çıktılar:
- Dashboard API endpoints
- Kapsamlı raporlama sistemi
- Analitik endpoints

---

### 🎨 AŞAMA 5: Frontend Altyapı (Chat 5)
**Süre:** 1-2 hafta  
**Hedef:** React/Next.js frontend altyapısını kurmak

#### Yapılacaklar:
1. **Proje Kurulumu:**
   - Next.js 14 + TypeScript kurulumu
   - Tailwind CSS konfigürasyonu
   - Klasör yapısı organizasyonu
   - ESLint, Prettier setup

2. **Temel Bileşenler:**
   - Layout components
   - Navigation (desktop/mobile)
   - Form components
   - Table components
   - Modal components

3. **State Management:**
   - Zustand store setup
   - TanStack Query konfigürasyonu
   - API client setup

4. **Authentication:**
   - Login/logout pages
   - Protected routes
   - JWT token management

#### Çıktılar:
- Çalışan Next.js uygulaması
- Temel UI bileşenleri
- Authentication sistemi
- API entegrasyonu

---

### 👥 AŞAMA 6: Core Frontend Modülleri (Chat 6)
**Süre:** 2 hafta  
**Hedef:** Müşteri, ürün, satış modüllerini frontend'de geliştirmek

#### Yapılacaklar:
1. **Müşteri Yönetimi:**
   - Müşteri listesi (grid)
   - Müşteri ekleme/düzenleme formları
   - Müşteri detay sayfası
   - Adres cascade dropdown

2. **Ürün Yönetimi:**
   - Ürün listesi
   - Ürün CRUD formları
   - Stok takip arayüzü

3. **Satış Yönetimi:**
   - Satış listesi
   - Satış ekleme/düzenleme
   - Satış detay sayfası
   - Teslim durumu yönetimi

#### Çıktılar:
- Müşteri yönetimi arayüzü
- Ürün yönetimi arayüzü
- Satış yönetimi arayüzü

---

### 🚛 AŞAMA 7: Gelişmiş Frontend Modülleri (Chat 7)
**Süre:** 1-2 hafta  
**Hedef:** Araç, biçme, gider, not modüllerini frontend'de tamamlamak

#### Yapılacaklar:
1. **Araç Yönetimi:**
   - Araç listesi ve formları
   - Yakıt takip arayüzü
   - Hatırlatma sistemi UI

2. **Biçme İşleri:**
   - Tarla yönetimi arayüzü
   - Kantar fişi formları
   - Hasat takip ekranları

3. **Gider ve Not Yönetimi:**
   - Gider listesi ve formları
   - Not yönetimi arayüzü
   - Filtreleme sistemleri

#### Çıktılar:
- Araç yönetimi arayüzü
- Biçme işleri arayüzü
- Gider/not yönetimi

---

### 📈 AŞAMA 8: Dashboard ve Raporlar (Chat 8)
**Süre:** 1 hafta  
**Hedef:** Dashboard ve raporlama arayüzlerini tamamlamak

#### Yapılacaklar:
1. **Dashboard:**
   - İstatistik kartları
   - Grafikler (Chart.js)
   - Son işlemler listeleri
   - Hatırlatma kartları

2. **Raporlama:**
   - Rapor filtreleme arayüzleri
   - Grafik gösterimleri
   - Export fonksiyonları
   - Print özelliği

#### Çıktılar:
- İnteraktif dashboard
- Kapsamlı raporlama arayüzü

---

### 🔧 AŞAMA 9: Optimizasyon ve Test (Chat 9)
**Süre:** 1 hafta  
**Hedef:** Performance optimizasyonu, test ve deployment

#### Yapılacaklar:
1. **Performance:**
   - API response optimizasyonu
   - Frontend lazy loading
   - Caching stratejileri

2. **Testing:**
   - Backend unit testleri
   - Frontend component testleri
   - Integration testleri

3. **Deployment:**
   - Docker containerization
   - Production konfigürasyonu
   - Environment variables

#### Çıktılar:
- Optimize edilmiş uygulama
- Test coverage
- Production-ready deployment

## 🎯 Her Aşama İçin Genel Kurallar

### Context Window Yönetimi:
- Her chat maksimum 3-4 modül
- Kod dosyaları 200 satırı geçmemeli
- Büyük dosyalar parçalara bölünmeli

### Kalite Standartları:
- Her endpoint için Pydantic schema
- Proper error handling
- Type hints kullanımı
- Docstring dokümantasyonu

### Test Stratejisi:
- Her modül için temel testler
- API endpoint testleri
- Frontend component testleri

## 📁 Proje Klasör Yapısı

```
kobipanel/
├── backend/
│   ├── app/
│   │   ├── models/
│   │   ├── schemas/
│   │   ├── api/
│   │   ├── core/
│   │   ├── db/
│   │   └── main.py
│   ├── alembic/
│   ├── tests/
│   └── requirements.txt
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── types/
│   │   └── utils/
│   ├── public/
│   └── package.json
└── docs/
```

## 📋 Detaylı Modül Listesi

### Backend Modelleri (SQLAlchemy):
1. **User** - Kullanıcı yönetimi
2. **Il, Ilce, Mahalle** - Adres sistemi
3. **Customer** - Müşteri bilgileri
4. **Product** - Ürün yönetimi
5. **Sale** - Satış işlemleri
6. **Expense** - Gider takibi
7. **Note** - Not yönetimi
8. **Vehicle** - Araç bilgileri
9. **Fuel** - Yakıt kayıtları
10. **CutField** - Biçilen tarlalar
11. **WeighingSlip** - Kantar fişleri

### API Endpoint Grupları:
- `/auth` - Authentication
- `/users` - Kullanıcı yönetimi
- `/customers` - Müşteri yönetimi
- `/products` - Ürün yönetimi
- `/sales` - Satış yönetimi
- `/expenses` - Gider yönetimi
- `/notes` - Not yönetimi
- `/vehicles` - Araç yönetimi
- `/cutting` - Biçme işleri
- `/reports` - Raporlama
- `/dashboard` - Dashboard verileri
- `/address` - Adres verileri

### Frontend Sayfaları:
- Dashboard (Ana sayfa)
- Müşteriler (Liste, Detay, Form)
- Satışlar (Liste, Detay, Form)
- Ürünler (Liste, Detay, Form)
- Giderler (Liste, Form)
- Notlar (Liste, Form)
- Araçlar (Liste, Detay, Form, Yakıt)
- Biçme İşleri (Tarlalar, Kantar Fişleri)
- Raporlar (Çeşitli raporlar)
- Kullanıcılar (Yönetim)
- Login/Logout

## 🔧 Teknik Detaylar

### Python Kütüphaneleri:
```
fastapi==0.104.1
sqlalchemy==2.0.23
alembic==1.12.1
pydantic==2.5.0
uvicorn==0.24.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
sqlite3 (built-in)
```

### Frontend Kütüphaneleri:
```
next==14.0.0
react==18.2.0
typescript==5.2.0
tailwindcss==3.3.0
@tanstack/react-query==5.0.0
zustand==4.4.0
react-hook-form==7.47.0
zod==3.22.0
@headlessui/react==1.7.0
chart.js==4.4.0
lucide-react==0.292.0
```

## ⚠️ Önemli Notlar

### Her Chat İçin:
1. **Maksimum 3-4 dosya** oluştur/düzenle
2. **Kod dosyaları 150-200 satır** ile sınırlı tut
3. **Test edilebilir** kod parçaları yaz
4. **Dokümantasyon** ekle
5. **Error handling** dahil et

### Veri Modeli Kuralları:
- Türkçe alan adları kullan (ad, soyad, tarih vs.)
- Foreign key ilişkilerini doğru kur
- Cascade delete ayarlarını belirle
- Index'leri unutma

### API Tasarım Kuralları:
- RESTful endpoint isimleri
- Proper HTTP status codes
- Pydantic response models
- Error response standardı

## 🚀 Başlangıç Komutu
İlk chat'te şu komutla başla:
**"AŞAMA 1: Backend Altyapı ve Temel Modüller - FastAPI proje kurulumu ve temel modelleri oluştur"**
