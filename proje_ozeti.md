# KobiPanel - Aydın Silaj Yönetim Sistemi Dokümantasyonu

## 🔄 Güncelleme Notları ve Tespit Edilen Farklılıklar

### Teknoloji Stack Güncellemeleri:
- **Backend:** Python FastAPI + SQLAlchemy olarak belirlendi
- **Veritabanı:** SQLite olarak kesinleştirildi
- **Proje <PERSON>ü<PERSON>:** Sıfırdan yeni geliştirme projesi (migration değil)

### Sonradan Eklenen Özellikler Tespit Edildi:
1. **Gelişmiş Araç Takibi:** Tescil no, şase no, vize/sigorta/egzoz tarihleri
2. **Mobil UX Gereksinimleri:** Alt navbar, 4 ana modül, tek kelimelik menüler
3. **Adres Modeli Tutarsızlığı:** District/Town çelişkisi düzeltildi

### Önerilen İyileştirmeler:
- Adres modelinde District veya Town'dan birini seçmek
- Mobil navigasyon gereksinimlerini UI/UX tasarım aşamasında önceliklendirmek
- Migration planını aşamalı olarak gerçekleştirmek

## Proje Genel Bilgileri

**Proje Adı:** KobiPanel - Aydın Silaj Yönetim Paneli
**Frontend:** React/Next.js + TypeScript + Tailwind CSS
**Backend:** Python FastAPI + SQLAlchemy
**Veritabanı:** SQLite
**Geliştirici:** İsmail Pehlevan

## Proje Amacı

Aydın Silaj firması için geliştirilmiş kapsamlı bir işletme yönetim sistemi. Tarımsal ürün satışları, müşteri yönetimi, araç takibi, biçme işleri, gider yönetimi ve raporlama işlevlerini içeren entegre bir platform.

**Not:** Bu proje Python FastAPI backend ve React/Next.js frontend ile sıfırdan geliştirilecek modern bir web uygulamasıdır.

## Ana Modüller ve Özellikler

### 1. Dashboard (Ana Sayfa)

- **Hızlı erişim. Kartları:**
  - En çok kullanılabilme olasılığı olan endpointler için direkt butonlar
  - **Hatırlatma kartları:**
  - Vize bitiş tarihi
  - Teslim edilmeyen satışlar gibi önemli durumlar hatırlatılıp kullanıcıdan aksiyon alması istenmeli.

- **İstatistik Kartları:**
  - Toplam müşteri sayısı
  - Toplam satış sayısı
  - Toplam ürün sayısı
  - Toplam gider sayısı
  - Toplam not sayısı
  - Toplam araç sayısı
  - Toplam tarla sayısı

- **Aylık İstatistikler:**
  - Bu ay satış sayısı
  - Bu ay gider sayısı
  - Aylık kar/zarar analizi
  - Grafik gösterimleri

- **Son İşlemler:**
  - Son satışlar listesi
  - Son giderler listesi
  - Son müşteriler listesi

### 2. Müşteri Yönetimi (Musteriler)
- **Müşteri Bilgileri:**
  - Ad Soyad
  - Telefon numarası
  - Yaş
  - Adres bilgileri (İl, İlçe, Mahalle)
  - Küçükbaş hayvan sayısı
  - Büyükbaş hayvan sayısı
  - Kayıt tarihi
  - Düzenlenme tarihi

- **İşlevler:**
  - Müşteri ekleme/düzenleme/silme
  - Müşteri detay görüntüleme -müşterilerin geçmiş satışları, bu satışlara ait notlar-
  - Müşteri listesi (Grid görünümü) -profesyonel detaylı arama ve sıralama-
  - Adres bilgileri (İl/İlçe/Mahalle cascade dropdown arama özellikli)

### 3. Satış Yönetimi (Satislar)
- **Satış Bilgileri:**
  - Ürün ID (Foreign Key)
  - Müşteri ID (Foreign Key)
  - Ürün adeti
  - Tutar
  - İndirim
  - Teslim şekli (Servis/Yerinde)
  - Ödeme tipi (Kredi kartı/Nakit)
  - Satış tarihi
  - Teslim tarihi (Adrese teslim ise, teslim edildiğinde otomatik atanır)
  - Teslim durumu
  - Nakliye bedeli
  - indirim tutarı -satış listeleme kısmında görünmeyecek. satış detay kısmında görünecek-
  - Açıklama

- **İşlevler:**
  - Satış ekleme/düzenleme/silme
  - Satış detay görüntüleme
  - Teslim edilmeyen satışlar
  - Satış özeti
  - Grid görünümü (filtreleme, sıralama)

### 4. Ürün Yönetimi (Urun)
- **Ürün Bilgileri:**
  - Ürün adı
  - ürün alış fiyatı
  - Ürün fiyatı
  - Mevcut stok
  - Nakliye ücreti
  - Eklenme tarihi
  - Düzenlenme tarihi

- **İşlevler:**
  - Ürün ekleme/düzenleme/silme
  - Ürün detay görüntüleme
  - Stok takibi
  - Ürün direkt olarak da girilebilir, Biçilen tarla eklendiyse oradaki hasat miktarı kadar yeni bir ürün oluşturulur.
  - Her parti ürün eşsiz bir tanıma sahip olmalı.
  - İlk giren ilk çıkar mantığı ile satış yapıldıkça stok azaltımı

### 5. Gider Yönetimi (Giderler)
- **Gider Bilgileri:**
  - Konu
  - Tutar
  - Tarih

- **İşlevler:**
  - Gider ekleme/düzenleme/silme
  - Gider kategorileri
  - Tarih bazlı filtreleme

### 6. Not Yönetimi (Not)
- **Not Bilgileri:**
  - Konu
  - Açıklama
  - Tarih

- **İşlevler:**
  - Not ekleme/düzenleme/silme
  - Not listesi

### 7. Araç Yönetimi (Araclar)
- **Araç Bilgileri:**
  - Plaka
  - Marka
  - Model
  - Yıl
  - Renk
  - Aktif durumu
  - Kayıt tarihi
  - Tescil no
  - Şase no
  - Vize bitiş tarihi - son 1 kala kaç gün sonra vizenin biteceği anasayfadaki hatırlatma kısmında vurgulanmalı -
  - Sigorta bitiş tarihi
  - Egzoz muayenesi bitiş tarihi

  *Not: Bu alanlar gelişmiş araç takibi için sonradan eklenen özelliklerdir.*

- **Yakıt Yönetimi (AlinanYakitlar):**
  - Araç ID (Foreign Key)
  - Litre
  - Yakıt tutarı
  - Litre başına fiyat
  - Yakıtın alındığı yer
  - Alış tarihi
  - Kayıt tarihi

- **İşlevler:**
  - Araç ekleme/düzenleme/silme
  - Yakıt kaydı ekleme/düzenleme/silme
  - Araç-yakıt ilişkisi

### 8. Biçme İşleri Yönetimi
- **Biçilen Tarlalar (BicilenTarlalar):**
  - Müşteri ID (Foreign Key)
  - Ürün ID (Foreign Key) -Kantar fişinin eklenmesi ile ürün stoklara eklenir-
  - Tarla adı
  - Ürün adı
  - Donum
  - Biçme fiyatı 
  - Toplam tutar
  - Tahsilat tutarı
  - Kalan tutar
  - Biçim tarihi
  - Açıklama
  - Toplam hasat miktarı (kg) - her kantar fişi eklendiğinde güncellenmeli -
  - Dönüm başına kg

- **Kantar Fişi (KantarFisi):**
  - Tarla ID (Foreign Key)
  - Müşteri ID (Foreign Key)
  - Ürün ID (Foreign Key)
  - Birinci tartım
  - İkinci tartım
  - Net kg
  - Tartım tarihi

- **İşlevler:**
  - Tarla ekleme/düzenleme/silme
  - Kantar fişi ekleme/düzenleme/silme (Ürün eklendiğinde otomatik olarak tarlanın hasat miktarı kadar ürün stoklara eklenmeli.)
  - Hasat takibi

### 9. Kullanıcı Yönetimi (Kullanicilar)
- **Kullanıcı Bilgileri:**
  - Kullanıcı adı
  - Şifre (SHA256 hash)
  - Ad Soyad
  - E-posta
  - Telefon -5554443322 formatında-
  - Rol (Yönetici/Kullanıcı/Misafir)
  - Aktif durumu
  - Son giriş tarihi
  - Kayıt tarihi
  - Güncelleme tarihi
  - Profil resmi
  - Açıklama

- **İşlevler:**
  - Kullanıcı ekleme/düzenleme/silme
  - Şifre değiştirme
  - Rol yönetimi
  - Kullanıcı aktivasyon/deaktivasyon

### 10. Raporlama Sistemi
- **Satış Raporları:**
  - Tarih aralığı filtreleme
  - Müşteri bazlı filtreleme
  - Ürün karlılık raporları
  - Toplam satış, tutar, ortalama hesaplamaları

- **Gider Raporları:**
  - Tarih aralığı filtreleme
  - Kategori bazlı filtreleme
  - Toplam gider analizi

- **Müşteri Raporları:**
  - Müşteri bazlı satış istatistikleri
  - Müşteri karlılık raporları
  - Toplam satış tutarı
  - Son satış tarihi

- **Ürün Raporları:**
  - Ürün bazlı satış istatistikleri
  - Toplam satış miktarı ve tutarı

- **Biçme Raporları:**
  - Toplam tarla sayısı
  - Toplam dönüm
  - Toplam tutar ve tahsilat
  - Kalan borç analizi

- **Araç Raporları:**
  - Araç bazlı yakıt tüketimi
  - Toplam yakıt tutarı
  - Yakıt alım sayısı

### 11. Adres Yönetimi
- **Adres Modelleri:**
  - Il (İl)
  - Ilce (İlçe)
  - Mahalle (Mahalle)

- **İşlevler:**
  - Cascade dropdown (İl → İlçe → Mahalle)
  - Adres bilgileri entegrasyonu

### 12. Güvenlik ve Loglama
- **Authentication:**
  - Forms Authentication
  - Session yönetimi -30dk sonra en son kullanıcıyı hatırlayarak sadece şifresini istemeli-
  - Login/Logout işlemleri

- **Authorization:**
  - Global AuthorizeAttribute
  - Rol bazlı erişim kontrolü

- **Loglama (ActFilter):**
  - Kullanıcı işlem logları
  - IP adresi takibi
  - Action/Controller logları
  - Tarih/saat bilgileri

- **Hata Yönetimi (ExcFilter):**
  - Exception handling
  - Error logging (ErrorLog tablosu)
  - Kullanıcı dostu hata mesajları
  - Stack trace kayıtları
  - Alınan hatalar özet olarak veritabanına kayıt edilmeli.

### 13. UI/UX Özellikleri
- **Framework:**
  - Tailwind CSS
  - React için componentler vs sınır yok. Mantık dahilinde sınır. 
  - Hoş tasarım için yeni frameworkler eklenebilir.
  - Font Awesome icons
  - jQuery

- **Tema Sistemi:**
  - Dark/Light mode toggle
  - Dinamik renk değişimi
  - Responsive tasarım
  - Tablet ve dokunmatik ekranlarda kullanıma uygun.
  - Kullanıcı fare kullanıyorsa farenin o anki konumunu kolaylıkla fark edebilmeli.

- **Navigation:**
  - Horizontal top navbar
  - Dropdown menüler
  - Active menu highlighting
  - Mobile responsive
  - Mobil navbar tamamen farklı olmalı. Telefon uygulaması gibi aşağıda olmalı. Hangisi seçildiyse seçili olduğu görsel olarak görüllmeli.
  - Mobil navbar menüleri : Satışlar,Müşteriler,Biçme İşleri,Notlar.
  - Mobil navbar içeriği : Sadece işin temeline uygun ikonlar ve tek kelime "Satış" "Müşteri","Biçme","Not" şeklinde olmalı.

  *Not: Mobil navigasyon özellikleri kullanıcı deneyimi iyileştirmesi için sonradan eklenen gereksinimlerdir.*

- **Grid Sistemi:**
  - React uyumlu profesyonel bir grid sistemi kullanılmalı.
  - Filtreleme ve sıralama
  - Sayfalama (pagination)
  - Ajax grid desteği

- **Form Özellikleri:**
  - Client-side validation
  - Server-side validation
  - Anti-forgery token
  - Kullanımı kolay hoş görünümlü

### 14. Veritabanı Yapısı
- **Ana Tablolar:**
  - Musteriler
  - Satislar
  - Urun
  - Giderler
  - Not
  - Kullanicilar

- **Araç Tabloları:**
  - Araclar
  - AlinanYakitlar

- **Biçme Tabloları:**
  - BicilenTarlalar
  - KantarFisi

- **Adres Tabloları:**
  - City
  - District
  - Town
  - Neighborhood

- **Sistem Tabloları:**
  - Log
  - ErrorLogs
  - Bildirimler

- **İlişkiler:**
  - Foreign Key constraints
  - Cascade delete ayarları
  - Navigation properties

## Teknik Özellikler

### Teknoloji Stack
**Backend:**
- Python 3.11+
- FastAPI
- SQLAlchemy (ORM)
- Pydantic (Data Validation)
- SQLite (Database)
- Alembic (Database Migrations)
- Uvicorn (ASGI Server)
- python-jose (JWT)
- passlib (Password Hashing)

**Frontend:**
- React/Next.js + TypeScript
- Tailwind CSS
- Zustand (State Management)
- React Hook Form + Zod (Forms & Validation)
- TanStack Query (Data Fetching)
- Headless UI / Radix UI (Components)
- Chart.js / Recharts (Charts)
- Lucide React (Icons)

## Önemli Notlar

1. **Null Reference Koruması:** Tüm controller'larda ve view'larda null reference hatalarına karşı koruma mevcuttur.

2. **Exception Handling:** Global exception filter ile tüm hatalar yakalanır ve loglanır.

3. **Validation:** Hem client-side hem server-side validation mevcuttur.

4. **Security:** SHA256 ile şifre hashleme, anti-forgery token kullanımı.

5. **Responsive Design:** Mobile-first yaklaşım ile responsive tasarım.

6. **Performance:** Grid sisteminde sayfalama ve filtreleme ile performans optimizasyonu.

7. **User Experience:** Material Design ile modern ve kullanıcı dostu arayüz.

8. **Logging:** Detaylı kullanıcı işlem logları ve hata logları.

9. **Modularity:** Modüler yapı ile kolay genişletilebilirlik.

10. **Data Integrity:** Foreign key constraints ile veri bütünlüğü korunur.

## API Endpoint'leri (Mevcut Controller Actions)

### HomeController
- `GET /` - Ana sayfa dashboard
- `GET /Home/Index` - Dashboard istatistikleri
- `GET /Home/Login` - Login sayfası
- `POST /Home/Login` - Login işlemi
- `GET /Home/LogOff` - Logout işlemi
- `GET /Home/GunOzeti` - Günlük özet

### MusteriController
- `GET /Musteri/MusteriAnasayfa` - Müşteri listesi
- `GET /Musteri/MusteriAnasayfaGrid` - Grid görünümü
- `GET /Musteri/MusteriDetay/{id}` - Müşteri detayı
- `GET /Musteri/MusteriEkle` - Müşteri ekleme formu
- `POST /Musteri/MusteriEkle` - Müşteri ekleme işlemi
- `GET /Musteri/MusteriDuzenle/{id}` - Müşteri düzenleme formu
- `POST /Musteri/MusteriDuzenle` - Müşteri düzenleme işlemi
- `GET /Musteri/MusteriSil/{id}` - Müşteri silme onayı
- `POST /Musteri/MusteriSil` - Müşteri silme işlemi
- `GET /Musteri/GetTowns/{cityId}` - İlçe listesi (Ajax)
- `GET /Musteri/GetNeighborhoods/{townId}` - Mahalle listesi (Ajax)

### SatisController
- `GET /Satis/SatisAnasayfa` - Satış listesi
- `GET /Satis/SatisAnasayfaGrid` - Grid görünümü
- `GET /Satis/SatisDetay/{id}` - Satış detayı
- `GET /Satis/SatisEkle` - Satış ekleme formu
- `POST /Satis/SatisEkle` - Satış ekleme işlemi
- `GET /Satis/SatisDuzenle/{id}` - Satış düzenleme formu
- `POST /Satis/SatisDuzenle` - Satış düzenleme işlemi
- `GET /Satis/SatisSil/{id}` - Satış silme onayı
- `POST /Satis/SatisSil` - Satış silme işlemi
- `GET /Satis/TeslimEdilmeyenSatis` - Teslim edilmeyen satışlar
- `GET /Satis/SatisOzeti` - Satış özeti

### UrunController
- `GET /Urun/UrunAnasayfa` - Ürün listesi
- `GET /Urun/UrunDetay/{id}` - Ürün detayı
- `GET /Urun/UrunEkle` - Ürün ekleme formu
- `POST /Urun/UrunEkle` - Ürün ekleme işlemi
- `GET /Urun/UrunDuzenle/{id}` - Ürün düzenleme formu
- `POST /Urun/UrunDuzenle` - Ürün düzenleme işlemi
- `GET /Urun/UrunSil/{id}` - Ürün silme onayı
- `POST /Urun/UrunSil` - Ürün silme işlemi

### GiderController
- `GET /Gider/GiderAnasayfa` - Gider listesi
- `GET /Gider/GiderEkle` - Gider ekleme formu
- `POST /Gider/GiderEkle` - Gider ekleme işlemi
- `GET /Gider/GiderDuzenle/{id}` - Gider düzenleme formu
- `POST /Gider/GiderDuzenle` - Gider düzenleme işlemi
- `GET /Gider/GiderSil/{id}` - Gider silme onayı
- `POST /Gider/GiderSil` - Gider silme işlemi

### NotController
- `GET /Not/NotAnasayfa` - Not listesi
- `GET /Not/NotEkle` - Not ekleme formu
- `POST /Not/NotEkle` - Not ekleme işlemi
- `GET /Not/NotDuzenle/{id}` - Not düzenleme formu
- `POST /Not/NotDuzenle` - Not düzenleme işlemi
- `GET /Not/NotSil/{id}` - Not silme onayı
- `POST /Not/NotSil` - Not silme işlemi

### AracController
- `GET /Arac/AracAnasayfa` - Araç listesi
- `GET /Arac/AracEkle` - Araç ekleme formu
- `POST /Arac/AracEkle` - Araç ekleme işlemi
- `GET /Arac/AracDuzenle/{id}` - Araç düzenleme formu
- `POST /Arac/AracDuzenle` - Araç düzenleme işlemi
- `GET /Arac/AracSil/{id}` - Araç silme onayı
- `POST /Arac/AracSil` - Araç silme işlemi
- `GET /Arac/YakitAnasayfa` - Yakıt listesi
- `GET /Arac/YakitEkle` - Yakıt ekleme formu
- `POST /Arac/YakitEkle` - Yakıt ekleme işlemi
- `GET /Arac/YakitDuzenle/{id}` - Yakıt düzenleme formu
- `POST /Arac/YakitDuzenle` - Yakıt düzenleme işlemi
- `GET /Arac/YakitSil/{id}` - Yakıt silme onayı
- `POST /Arac/YakitSil` - Yakıt silme işlemi

### BicmeController
- `GET /Bicme/TarlaAnasayfa` - Tarla listesi
- `GET /Bicme/TarlaDetay/{id}` - Tarla detayı
- `GET /Bicme/TarlaEkle` - Tarla ekleme formu
- `POST /Bicme/TarlaEkle` - Tarla ekleme işlemi
- `GET /Bicme/TarlaDuzenle/{id}` - Tarla düzenleme formu
- `POST /Bicme/TarlaDuzenle` - Tarla düzenleme işlemi
- `GET /Bicme/TarlaSil/{id}` - Tarla silme onayı
- `POST /Bicme/TarlaSil` - Tarla silme işlemi
- `GET /Bicme/KantarFisiAnasayfa` - Kantar fişi listesi
- `GET /Bicme/KantarFisiDetay/{id}` - Kantar fişi detayı
- `GET /Bicme/KantarFisiEkle` - Kantar fişi ekleme formu
- `POST /Bicme/KantarFisiEkle` - Kantar fişi ekleme işlemi
- `GET /Bicme/KantarFisiDuzenle/{id}` - Kantar fişi düzenleme formu
- `POST /Bicme/KantarFisiDuzenle` - Kantar fişi düzenleme işlemi
- `GET /Bicme/KantarFisiSil/{id}` - Kantar fişi silme onayı
- `POST /Bicme/KantarFisiSil` - Kantar fişi silme işlemi

### KullaniciController
- `GET /Kullanici/Index` - Kullanıcı listesi
- `GET /Kullanici/Details/{id}` - Kullanıcı detayı
- `GET /Kullanici/Create` - Kullanıcı ekleme formu
- `POST /Kullanici/Create` - Kullanıcı ekleme işlemi
- `GET /Kullanici/Edit/{id}` - Kullanıcı düzenleme formu
- `POST /Kullanici/Edit` - Kullanıcı düzenleme işlemi
- `GET /Kullanici/Delete/{id}` - Kullanıcı silme onayı
- `POST /Kullanici/Delete` - Kullanıcı silme işlemi
- `GET /Kullanici/ChangePassword/{id}` - Şifre değiştirme formu
- `POST /Kullanici/ChangePassword` - Şifre değiştirme işlemi

### RaporController
- `GET /Rapor/Index` - Rapor merkezi
- `GET /Rapor/SatisRaporlari` - Satış raporları
- `GET /Rapor/GiderRaporlari` - Gider raporları
- `GET /Rapor/MusteriRaporlari` - Müşteri raporları
- `GET /Rapor/UrunRaporlari` - Ürün raporları
- `GET /Rapor/BicmeRaporlari` - Biçme raporları
- `GET /Rapor/AracRaporlari` - Araç raporları

## Veri Modelleri (TypeScript Interfaces)

### Müşteri (Customer)
```typescript
interface Customer {
  id: number;
  adsoyad: string;
  tel: string;
  kayitTarihi: Date;
  duzenlemeTarihi: Date;
  yas?: number;
  cityId?: number;
  townId?: number;
  neighborhoodId?: number;
  kucukBasHayvanSayisi?: number;
  buyukBasHayvanSayisi?: number;
  city?: City;
  town?: Town;
  neighborhood?: Neighborhood;
}
```

### Satış (Sale)
```typescript
interface Sale {
  satisId: number;
  urunId: number;
  musteriId: number;
  urunAdeti: number;
  tutar: number;
  indirim: number;
  servisMi: boolean; //true ise adrese teslim. false ise yerinde teslim.
  krediKartiMi: boolean;
  satisTarihi: Date;
  teslimEdildiMi?: boolean;
  nakliyeBedeli: number;
  aciklama: string;
  urun?: Product;
  musteri?: Customer;
}
```

### Ürün (Product)
```typescript
interface Product {
  urunId: number;
  urunAdi: string;
  urunFiyati: number;
  mevcutStok: number;
  nakliyeUcreti: number;
  eklenmeTarihi: Date;
  duzenlenmeTarihi: Date;
}
```

### Gider (Expense)
```typescript
interface Expense {
  id: number;
  konu: string;
  tutar?: number;
  tarih: Date;
}
```

### Not (Note)
```typescript
interface Note {
  id: number;
  konu: string;
  aciklama: string;
  tarih: Date;
}
```

### Araç (Vehicle)
```typescript
interface Vehicle {
  id: number;
  plaka: string;
  marka: string;
  model: string;
  yil: number;
  renk: string;
  aktif: boolean;
  kayitTarihi: Date;
  yakitlar?: Fuel[];
}
```

### Yakıt (Fuel)
```typescript
interface Fuel {
  id: number;
  aracId: number;
  litre: number;
  yakitTutari: number;
  litreBasinaFiyat: number;
  yakitinAlindigiYer: string;
  alisTarihi: Date;
  kayitTarihi: Date;
  arac?: Vehicle;
}
```

### Biçilen Tarla (CutField)
```typescript
interface CutField {
  id: number;
  musteriId: number;
  tarlaAdi: string;
  donum: number;
  bicmeFiyati: number;
  toplamTutar: number;
  tahsilatTutari: number;
  kalanTutar: number;
  bicimTarihi: Date;
  aciklama: string;
  toplamHasatMiktari: number;
  donumBasinaKg: number;
  musteri?: Customer;
  kantarFisleri?: WeighingSlip[];
}
```

### Kantar Fişi (WeighingSlip)
```typescript
interface WeighingSlip {
  id: number;
  tarlaId: number;
  musteriId: number;
  birinciTartim: number;
  ikinciTartim: number;
  netKg: number;
  tartimTarihi: Date;
  tarla?: CutField;
  musteri?: Customer;
}
```

### Kullanıcı (User)
```typescript
interface User {
  id: number;
  kullaniciAdi: string;
  sifre: string;
  adSoyad: string;
  email: string;
  telefon?: string;
  rol: string;
  aktif: boolean;
  sonGirisTarihi?: Date;
  kayitTarihi: Date;
  guncellemeTarihi?: Date;
  profilResmi?: string;
  aciklama?: string;
}
```

### Adres Modelleri
```typescript
interface Il {
  id: number;
  ad: string;
}

interface Ilce {
  id: number;
  ad: string;
  il_id: number;
}

interface Mahalle {
  id: number;
  ad: string;
  ilce_id: number;
}
```

## React/Next.js Sayfa Yapısı Önerisi

### Ana Dizin Yapısı
```
src/
├── components/
│   ├── ui/
│   ├── forms/
│   ├── tables/
│   ├── charts/
│   └── layout/
├── pages/
│   ├── dashboard/
│   ├── customers/
│   ├── sales/
│   ├── products/
│   ├── expenses/
│   ├── notes/
│   ├── vehicles/
│   ├── cutting/
│   ├── users/
│   ├── reports/
│   └── auth/
├── hooks/
├── services/
├── types/
├── utils/
└── styles/
```

### Sayfa Rotaları
```
/ - Dashboard
/customers - Müşteri listesi
/customers/new - Yeni müşteri
/customers/[id] - Müşteri detayı
/customers/[id]/edit - Müşteri düzenleme

/sales - Satış listesi
/sales/new - Yeni satış
/sales/[id] - Satış detayı
/sales/[id]/edit - Satış düzenleme

/products - Ürün listesi
/products/new - Yeni ürün
/products/[id] - Ürün detayı
/products/[id]/edit - Ürün düzenleme

/expenses - Gider listesi
/expenses/new - Yeni gider
/expenses/[id]/edit - Gider düzenleme

/notes - Not listesi
/notes/new - Yeni not
/notes/[id]/edit - Not düzenleme

/vehicles - Araç listesi
/vehicles/new - Yeni araç
/vehicles/[id] - Araç detayı
/vehicles/[id]/edit - Araç düzenleme
/vehicles/[id]/fuel - Yakıt kayıtları

/cutting/fields - Tarla listesi
/cutting/fields/new - Yeni tarla
/cutting/fields/[id] - Tarla detayı
/cutting/fields/[id]/edit - Tarla düzenleme
/cutting/weighing - Kantar fişleri
/cutting/weighing/new - Yeni kantar fişi

/users - Kullanıcı listesi
/users/new - Yeni kullanıcı
/users/[id] - Kullanıcı detayı
/users/[id]/edit - Kullanıcı düzenleme

/reports - Rapor merkezi
/reports/sales - Satış raporları
/reports/expenses - Gider raporları
/reports/customers - Müşteri raporları
/reports/products - Ürün raporları
/reports/cutting - Biçme raporları
/reports/vehicles - Araç raporları

/auth/login - Giriş
/auth/logout - Çıkış
```

## Geliştirme Önerileri ve İyileştirmeler

### 1. Modern UI/UX İyileştirmeleri
- **Responsive Design:** Mobile-first yaklaşım ile tüm cihazlarda optimum deneyim
- **Dark/Light Theme:** Sistem tercihi ile otomatik tema değişimi
- **Loading States:** Skeleton loaders ve progress indicators
- **Error Boundaries:** Hata durumlarında kullanıcı dostu mesajlar
- **Toast Notifications:** İşlem sonuçları için modern bildirimler
- **Modal Dialogs:** Silme onayları ve form dialogs
- **Data Tables:** Sorting, filtering, pagination ile gelişmiş tablolar
- **Charts & Graphs:** Dashboard için interaktif grafikler

### 2. Performance Optimizasyonları
- **Code Splitting:** Route-based ve component-based lazy loading
- **Image Optimization:** Next.js Image component kullanımı
- **Caching:** React Query/SWR ile intelligent caching
- **Virtual Scrolling:** Büyük listeler için performans optimizasyonu
- **Bundle Analysis:** Webpack bundle analyzer ile optimizasyon
- **SEO Optimization:** Meta tags ve structured data

### 3. Developer Experience İyileştirmeleri
- **TypeScript:** Strict mode ile type safety
- **ESLint & Prettier:** Code quality ve formatting
- **Husky & Lint-staged:** Pre-commit hooks
- **Storybook:** Component documentation
- **Testing:** Jest + React Testing Library
- **CI/CD:** GitHub Actions ile automated deployment

### 4. Security Enhancements
- **JWT Authentication:** Secure token-based auth
- **RBAC:** Role-based access control
- **Input Validation:** Zod schemas ile validation
- **CSRF Protection:** Cross-site request forgery koruması
- **Rate Limiting:** API endpoint koruması
- **Audit Logging:** Detaylı kullanıcı işlem logları

### 5. API Design
- **RESTful API:** Consistent endpoint design
- **GraphQL:** Complex queries için alternatif
- **API Versioning:** Backward compatibility
- **OpenAPI/Swagger:** API documentation
- **Error Handling:** Standardized error responses
- **Pagination:** Cursor-based pagination

## Implementasyon Planı

### Faz 1: Temel Altyapı (2-3 hafta)
1. **Proje Setup:**
   - Next.js 14 + TypeScript kurulumu
   - Tailwind CSS konfigürasyonu
   - ESLint, Prettier, Husky setup
   - Folder structure oluşturma

2. **Authentication System:**
   - NextAuth.js entegrasyonu
   - JWT token management
   - Login/logout pages
   - Protected routes

3. **Database & API:**
   - Prisma ORM setup
   - Database schema migration
   - Basic CRUD API endpoints
   - Error handling middleware

### Faz 2: Core Components (3-4 hafta)
1. **Layout Components:**
   - Navigation bar
   - Sidebar
   - Footer
   - Theme provider

2. **UI Components:**
   - Form components
   - Table components
   - Modal components
   - Button variants
   - Input variants

3. **Dashboard:**
   - Statistics cards
   - Charts integration
   - Recent activities
   - Quick actions

### Faz 3: Business Logic (4-5 hafta)
1. **Customer Management:**
   - Customer CRUD operations
   - Address management
   - Customer details page

2. **Sales Management:**
   - Sales CRUD operations
   - Product selection
   - Customer selection
   - Sales summary

3. **Product Management:**
   - Product CRUD operations
   - Stock management
   - Product categories

### Faz 4: Advanced Features (3-4 hafta)
1. **Vehicle Management:**
   - Vehicle CRUD operations
   - Fuel tracking
   - Vehicle reports

2. **Cutting Operations:**
   - Field management
   - Weighing slips
   - Harvest tracking

3. **Expense & Notes:**
   - Expense tracking
   - Note management
   - Categories

### Faz 5: Reporting & Analytics (2-3 hafta)
1. **Report System:**
   - Sales reports
   - Expense reports
   - Customer analytics
   - Product analytics

2. **Charts & Visualizations:**
   - Chart.js/Recharts integration
   - Interactive dashboards
   - Export functionality

### Faz 6: Testing & Deployment (2 hafta)
1. **Testing:**
   - Unit tests
   - Integration tests
   - E2E tests
   - Performance testing

2. **Deployment:**
   - Production build optimization
   - Vercel/Netlify deployment
   - Environment configuration
   - Monitoring setup

## Teknoloji Stack Detayları

### Frontend
```json
{
  "framework": "Next.js 14",
  "language": "TypeScript",
  "styling": "Tailwind CSS",
  "ui-library": "Headless UI / Radix UI",
  "state-management": "Zustand / Redux Toolkit",
  "forms": "React Hook Form + Zod",
  "data-fetching": "TanStack Query (React Query)",
  "charts": "Chart.js / Recharts",
  "icons": "Lucide React / Heroicons",
  "animations": "Framer Motion"
}
```

### Backend API
```json
{
  "runtime": "Node.js",
  "framework": "Next.js API Routes",
  "database": "PostgreSQL / MySQL",
  "orm": "Prisma",
  "authentication": "NextAuth.js",
  "validation": "Zod",
  "file-upload": "Uploadthing / Cloudinary"
}
```

### Development Tools
```json
{
  "package-manager": "pnpm",
  "linting": "ESLint",
  "formatting": "Prettier",
  "testing": "Jest + React Testing Library",
  "e2e-testing": "Playwright",
  "documentation": "Storybook",
  "deployment": "Vercel / Docker"
}
```

## Önemli Notlar ve Dikkat Edilecek Noktalar

1. **Veri Migrasyonu:** Mevcut SQL Server veritabanından yeni sisteme veri aktarımı planlanmalı
2. **Backward Compatibility:** Mevcut kullanıcıların alışkın olduğu workflow korunmalı
3. **Performance:** Büyük veri setleri için pagination ve lazy loading kritik
4. **Mobile Experience:** Tarla çalışanları için mobile-friendly design önemli
5. **Offline Support:** PWA özellikleri ile offline çalışma desteği
6. **Backup Strategy:** Otomatik backup ve disaster recovery planı
7. **User Training:** Yeni sistem için kullanıcı eğitimi planlanmalı
8. **Gradual Migration:** Aşamalı geçiş stratejisi ile risk minimizasyonu

## Sonuç

Bu dokümantasyon, Aydın Silaj KobiPanel projesinin modern web teknolojileri ile yeniden yazılması için kapsamlı bir rehber sunmaktadır. Mevcut sistemin tüm işlevselliği korunarak, modern UI/UX, performans optimizasyonları ve gelişmiş güvenlik özellikleri ile güçlendirilmiş bir platform oluşturulacaktır.

Proje toplam 16-19 hafta sürmesi öngörülmekte olup, aşamalı geliştirme yaklaşımı ile risk minimize edilecektir. Her faz sonunda test edilebilir ve kullanılabilir özellikler teslim edilecektir.
